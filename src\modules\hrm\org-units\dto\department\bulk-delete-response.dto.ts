import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho kết quả xóa nhiều phòng ban
 */
export class BulkDeleteResponseDto {
  /**
   * Tổng số phòng ban được yêu cầu xóa
   */
  @ApiProperty({
    description: 'Tổng số phòng ban được yêu cầu xóa',
    example: 5,
    type: Number,
  })
  totalRequested: number;

  /**
   * Số phòng ban đã xóa thành công
   */
  @ApiProperty({
    description: 'Số phòng ban đã xóa thành công',
    example: 3,
    type: Number,
  })
  successCount: number;

  /**
   * Số phòng ban không thể xóa
   */
  @ApiProperty({
    description: 'Số phòng ban không thể xóa',
    example: 2,
    type: Number,
  })
  failureCount: number;

  /**
   * Danh sách ID các phòng ban đã xóa thành công
   */
  @ApiProperty({
    description: 'Danh sách ID các phòng ban đã xóa thành công',
    example: [1, 3, 5],
    type: [Number],
  })
  deletedIds: number[];

  /**
   * Danh sách ID các phòng ban không thể xóa và lý do
   */
  @ApiProperty({
    description: 'Danh sách ID các phòng ban không thể xóa và lý do',
    example: [
      { id: 2, reason: 'Phòng ban có phòng ban con trực thuộc' },
      { id: 4, reason: 'Không tìm thấy phòng ban' }
    ],
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'number' },
        reason: { type: 'string' }
      }
    }
  })
  failures: Array<{ id: number; reason: string }>;
}
