import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddUserIdToEmployees1703000000001 implements MigrationInterface {
  name = 'AddUserIdToEmployees1703000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột user_id vào bảng employees
    await queryRunner.addColumn(
      'employees',
      new TableColumn({
        name: 'user_id',
        type: 'integer',
        isNullable: true,
      }),
    );

    // Tạo index cho cột user_id để tăng hiệu suất truy vấn
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_employees_user_id 
      ON employees(user_id)
    `);

    // Tạo unique constraint cho user_id trong cùng tenant
    await queryRunner.query(`
      CREATE UNIQUE INDEX IF NOT EXISTS idx_employees_user_id_tenant_unique 
      ON employees(user_id, tenant_id) 
      WHERE user_id IS NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa các index trước
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_employees_user_id_tenant_unique
    `);
    
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_employees_user_id
    `);
    
    // Xóa cột user_id
    await queryRunner.dropColumn('employees', 'user_id');
  }
}
